'use client'

import React, {
  useCallback,
  useEffect, useState, useRef,
} from 'react';
import Konva from 'konva';
import {
  AlignLeft,
  AlignCenter,
  AlignRight,
} from 'lucide-react';
import toast from 'react-hot-toast';
import { Button } from '../../../atoms';
import { Dropdown } from '../../../molecules';

interface TextPanelProps {
  canvas: Konva.Stage | null;
  agentId?: string;
  planId?: string;
  containerRef?: React.RefObject<HTMLDivElement>;
  zoomLevel?: number;
}

export const TextPanel = ({
  canvas,
  containerRef,
}: TextPanelProps) => {
  const [fontFamily, setFontFamily] = useState('Arial');
  const [fontSize, setFontSize] = useState(24);
  const [textColor, setTextColor] = useState('#000000');
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [textAlign, setTextAlign] = useState('center');
  const [isEditing, setIsEditing] = useState(false);
  const [editingText, setEditingText] = useState('');
  const [editingNode, setEditingNode] = useState<Konva.Text | null>(null);
  const [textInputPosition, setTextInputPosition] = useState({ x: 0, y: 0 });
  const textInputRef = useRef<HTMLTextAreaElement>(null);

  const fonts = [
    'Arial',
    'Brush Script MT',
    'Chillax',
    'Courier New',
    'Georgia',
    'Impact',
    'Tahoma',
    'Trebuchet MS',
    'Verdana',
  ];

  // Function to start editing text
  const startTextEditing = useCallback((textNode: Konva.Text) => {
    if (!canvas || !containerRef?.current) return;

    const stage = canvas;
    const container = containerRef.current;
    const containerRect = container.getBoundingClientRect();

    // Get text position relative to the container
    const textPosition = textNode.getAbsolutePosition();
    const stagePosition = stage.getAbsolutePosition();

    // Calculate position for the text input
    const x = textPosition.x + stagePosition.x - containerRect.left;
    const y = textPosition.y + stagePosition.y - containerRect.top;

    setEditingNode(textNode);
    setEditingText(textNode.text());
    setTextInputPosition({ x, y });
    setIsEditing(true);

    // Focus the input after a short delay
    setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
        textInputRef.current.select();
      }
    }, 10);
  }, [canvas, containerRef]);

  // Function to finish editing text
  const finishTextEditing = useCallback(() => {
    if (editingNode && editingText !== undefined) {
      editingNode.text(editingText);
      canvas?.batchDraw();
    }
    setIsEditing(false);
    setEditingNode(null);
    setEditingText('');
  }, [editingNode, editingText, canvas]);

  // Handle text input changes
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditingText(e.target.value);
  };

  // Handle key press in text input
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      finishTextEditing();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditingNode(null);
      setEditingText('');
    }
  };

  const addText = () => {
    if (!canvas) {
      return;
    }

    // Get or create the main layer
    let layer = canvas.findOne('Layer') as Konva.Layer;
    if (!layer) {
      layer = new Konva.Layer();
      canvas.add(layer);
    }

    const text = new Konva.Text({
      x: 100,
      y: 100,
      text: 'Double-click to edit',
      fontFamily: fontFamily,
      fontSize: fontSize,
      fill: textColor,
      fontStyle: `${isBold ? 'bold' : 'normal'} ${isItalic ? 'italic' : 'normal'}`,
      align: textAlign as 'left' | 'center' | 'right',
      draggable: true,
    });

    // Add double-click event handler for text editing
    text.on('dblclick', () => {
      startTextEditing(text);
    });

    layer.add(text);

    // Create or get transformer for selection
    let transformer = layer.findOne('Transformer') as Konva.Transformer;
    if (!transformer) {
      transformer = new Konva.Transformer();
      layer.add(transformer);
    }

    transformer.nodes([text]);
    canvas.batchDraw();
    toast.success('Text added to canvas!');
  };

  const updateSelectedText = useCallback(() => {
    if (!canvas) {
      return;
    }

    const transformer = canvas.findOne('Transformer') as Konva.Transformer;
    if (transformer) {
      const selectedNodes = transformer.nodes();
      selectedNodes.forEach((node) => {
        if (node.getClassName() === 'Text') {
          const textNode = node as Konva.Text;
          textNode.fontFamily(fontFamily);
          textNode.fontSize(fontSize);
          textNode.fill(textColor);
          textNode.fontStyle(`${isBold ? 'bold' : 'normal'} ${isItalic ? 'italic' : 'normal'}`);
          textNode.align(textAlign as 'left' | 'center' | 'right');
        }
      });
      canvas.batchDraw();
    }
  }, [canvas, fontFamily, fontSize, textColor, isBold, isItalic, textAlign]);

  useEffect(() => {
    updateSelectedText();
  }, [fontFamily, fontSize, textColor, isBold, isItalic, textAlign, updateSelectedText]);

  // Add double-click handlers to existing text elements
  useEffect(() => {
    if (!canvas) return;

    const addDoubleClickToTextNodes = () => {
      const layer = canvas.findOne('Layer') as Konva.Layer;
      if (!layer) return;

      layer.find('Text').forEach((node) => {
        const textNode = node as Konva.Text;
        // Remove existing listeners to avoid duplicates
        textNode.off('dblclick');
        // Add double-click handler
        textNode.on('dblclick', () => {
          startTextEditing(textNode);
        });
      });
    };

    addDoubleClickToTextNodes();

    // Re-add handlers when the canvas changes
    const handleCanvasChange = () => {
      setTimeout(addDoubleClickToTextNodes, 100);
    };

    canvas.on('dragend', handleCanvasChange);
    canvas.on('transformend', handleCanvasChange);

    return () => {
      canvas.off('dragend', handleCanvasChange);
      canvas.off('transformend', handleCanvasChange);
    };
  }, [canvas, startTextEditing]);

  return (
    <div className="px-6 py-4">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg">Add Text</h3>
        <p className="text-gray-400 text-sm">Add and customize text</p>
      </div>
      <div className="space-y-6">
       

        <div className="space-y-4">
          <div>
            <Dropdown
              label="Font Family"
              options={fonts.map(font => ({ 
                value: font, 
                label: font,
              }))}
              selectedOption={{ 
                label: fontFamily, 
                option: fontFamily,
              }}
              onSelect={(option) => {
                setFontFamily(option.label)
              }}
              placeholder="Select Font Style"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Font Size: {fontSize}px
            </label>
            <input
              type="range"
              min="12"
              max="120"
              value={fontSize}
              onChange={(e) => setFontSize(Number(e.target.value))}
              className="w-full accent-violets-are-blue"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Color</label>
            <input
              type="color"
              value={textColor}
              onChange={(e) => setTextColor(e.target.value)}
              className="w-full h-10 rounded-lg p-0.5 py-0 border border-neutral-600"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Text Style</label>
            <div className="flex gap-2">
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setIsBold(!isBold)}
                className={`${isBold ? "text-violets-are-blue !border-violets-are-blue" : ""}`}
              >
                Bold
              </Button>
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setIsItalic(!isItalic)}
                className={`${isItalic ? "text-violets-are-blue !border-violets-are-blue" : ""}`}
              >
                Italic
              </Button>
            </div>
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Text Alignment</label>
            <div className="flex gap-2">
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setTextAlign('left')}
                className={`${textAlign === 'left' ? "text-violets-are-blue !border-violets-are-blue" : "!text-gray-500 !border-gray-500 hover:!text-white"} !px-3`}
                title="Align Left"
              >
                <AlignLeft size={16} />
              </Button>
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setTextAlign('center')}
                className={`${textAlign === 'center' ? "text-violets-are-blue !border-violets-are-blue" : "!text-gray-500 !border-gray-500 hover:!text-white"} !px-3`}
                title="Align Center"
              >
                <AlignCenter size={16} />
              </Button>
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setTextAlign('right')}
                className={`${textAlign === 'right' ? "text-violets-are-blue !border-violets-are-blue" : "!text-gray-500 !border-gray-500 hover:!text-white"} !px-3`}
                title="Align Right"
              >
                <AlignRight size={16} />
              </Button>
            </div>
          </div>
        </div>
        <Button
          variant="gradient"
          size="md"
          width="w-full"
          onClick={addText}
        >
          Add Text
        </Button>
        <div className="text-xs text-gray-500 bg-neutral-800 p-3 rounded-xl mt-auto">
          <p className="mb-1">💡 <strong>Tips:</strong></p>
          <p>• Select text on canvas to modify style & alignment</p>
          <p>• Double-click text to edit content</p>
          <p>• Use Delete key to remove selected text</p>
        </div>
      </div>

      {/* Text editing overlay */}
      {isEditing && containerRef?.current && (
        <div
          style={{
            position: 'absolute',
            left: textInputPosition.x,
            top: textInputPosition.y,
            zIndex: 1000,
          }}
        >
          <textarea
            ref={textInputRef}
            value={editingText}
            onChange={handleTextChange}
            onKeyDown={handleKeyPress}
            onBlur={finishTextEditing}
            className="bg-white text-black p-2 border border-gray-300 rounded resize-none"
            style={{
              fontFamily: editingNode?.fontFamily() || 'Arial',
              fontSize: `${editingNode?.fontSize() || 24}px`,
              fontWeight: editingNode?.fontStyle()?.includes('bold') ? 'bold' : 'normal',
              fontStyle: editingNode?.fontStyle()?.includes('italic') ? 'italic' : 'normal',
              textAlign: editingNode?.align() as 'left' | 'center' | 'right' || 'left',
              minWidth: '100px',
              minHeight: '30px',
            }}
            autoFocus
          />
        </div>
      )}
    </div>
  );
};

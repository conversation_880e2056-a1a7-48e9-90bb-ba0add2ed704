'use client'

import { Button } from '@/common/components/atoms';
import { motion } from "framer-motion";
import Link from 'next/link';
import { routes } from '@/common/routes';
import { ClientLogo } from '../header/ClientLogo';
import { secondaryFont } from '@/common/utils/localFont';

interface CanvasHeaderProps {
  onSaveDesign: () => void;
}

export const CanvasHeader = ({
  onSaveDesign,
}: CanvasHeaderProps) => {

  return (
    <div className="bg-eerie-black border-b border-neutral-700 px-4 py-2 md:py-3 flex items-center justify-between relative">
      <div className="flex items-center gap-1 md:gap-2">
        <Link href={routes.homePath} prefetch={true} replace className="flex gap-1 md:gap-2 items-center text-white font-semibold text-xl md:text-2xl">
          <ClientLogo width={24} height={24}/>
          <motion.span
            initial={{
              opacity: 0,
              translateX: 20,
            }}
            animate={{
              opacity: 1,
              translateX: 0,
            }}
            transition={{
              duration: 0.5,
              delay: 1,
            }}
            className={`text-white ${secondaryFont.className} animate-glowTransition`}
          >
            Media Pilot
          </motion.span>
        </Link>
      </div>
      <div className="flex items-center gap-1 md:gap-2">
        <Button
          variant="gradient"
          size="sm"
          onClick={onSaveDesign}
        >
          Save to Post
        </Button>
      </div>
    </div>
  );
};
